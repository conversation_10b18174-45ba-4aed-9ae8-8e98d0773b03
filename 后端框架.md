# 项目笔记管理系统 - 后端框架设计

## 1. 后端技术架构

### 1.1 技术栈选择
- **后端框架**: Node.js + Express.js
- **数据库**: SQLite3
- **ORM**: 原生 sqlite3 模块
- **API 规范**: RESTful API
- **身份验证**: JWT (JSON Web Token)
- **跨域处理**: CORS
- **日志管理**: Winston
- **测试框架**: Jest + Supertest

### 1.2 目录结构
```
backend/
├── src/
│   ├── controllers/         # 控制器层
│   ├── models/             # 数据模型层
│   ├── routes/             # 路由层
│   ├── middleware/         # 中间件
│   ├── services/           # 业务逻辑层
│   ├── utils/              # 工具函数
│   ├── config/             # 配置文件
│   └── database/           # 数据库相关
├── tests/                  # 测试文件
├── docs/                   # API文档
├── logs/                   # 日志文件
└── uploads/                # 文件上传目录
```

## 2. 数据库设计

### 2.1 数据表结构

#### 2.1.1 目录表 (categories)
```sql
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.1.2 笔记表 (notes)
```sql
CREATE TABLE notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category_id INTEGER NOT NULL,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    is_important BOOLEAN DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);
```

## 3. 后端代码文件结构

### 3.1 应用入口文件

#### 文件: `src/app.js`

**代码文件功能说明**:
- Express应用程序的主入口文件
- 配置中间件、路由、错误处理
- 初始化数据库连接
- 启动HTTP服务器

**代码文件依赖的需要格外安装的库**:
```json
{
  "express": "^4.18.2",
  "cors": "^2.8.5",
  "helmet": "^7.0.0",
  "compression": "^1.7.4",
  "express-rate-limit": "^6.8.1",
  "dotenv": "^16.3.1",
  "winston": "^3.10.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/routes/index.js` 文件中的 `router` 变量 (Express路由配置实例)
- `src/middleware/errorHandler.js` 文件中的 `errorHandler` 函数 (全局错误处理中间件)
- `src/middleware/corsOptions.js` 文件中的 `corsOptions` 对象 (CORS跨域配置选项)
- `src/config/database.js` 文件中的 `initDatabase` 函数 (数据库初始化方法)
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `app`: Express应用实例
- `server`: HTTP服务器实例

---

### 3.2 数据库配置和初始化

#### 文件: `src/config/database.js`

**代码文件功能说明**:
- 配置SQLite数据库连接
- 创建数据表结构
- 提供数据库操作的基础方法

**代码文件依赖的需要格外安装的库**:
```json
{
  "sqlite3": "^5.1.6",
  "path": "内置模块"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录数据库操作日志)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `db`: SQLite数据库连接实例
- `initDatabase()`: 初始化数据库函数
- `closeDatabase()`: 关闭数据库连接函数

---

### 3.3 数据模型层

#### 文件: `src/models/Category.js`

**代码文件功能说明**:
- 目录数据模型，封装目录相关的数据库操作
- 提供CRUD操作方法
- 处理目录排序逻辑

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/config/database.js` 文件中的 `db` 变量 (SQLite数据库连接实例，用于执行目录相关的SQL查询)
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录目录模型操作日志)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `Category`: 目录模型类
- `getAllCategories()`: 获取所有目录
- `getCategoryById(id)`: 根据ID获取目录
- `createCategory(data)`: 创建新目录
- `updateCategory(id, data)`: 更新目录
- `deleteCategory(id)`: 删除目录
- `updateCategoryOrder(id, sortOrder)`: 更新目录排序

#### 文件: `src/models/Note.js`

**代码文件功能说明**:
- 笔记数据模型，封装笔记相关的数据库操作
- 提供CRUD操作方法
- 处理笔记排序和重要性标记

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/config/database.js` 文件中的 `db` 变量 (SQLite数据库连接实例，用于执行笔记相关的SQL查询)
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录笔记模型操作日志)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `Note`: 笔记模型类
- `getAllNotes()`: 获取所有笔记
- `getNotesByCategory(categoryId)`: 根据目录ID获取笔记
- `getNoteById(id)`: 根据ID获取笔记
- `createNote(data)`: 创建新笔记
- `updateNote(id, data)`: 更新笔记
- `deleteNote(id)`: 删除笔记
- `updateNoteOrder(id, sortOrder)`: 更新笔记排序
- `toggleNoteImportance(id)`: 切换笔记重要性标记

---

### 3.4 业务逻辑服务层

#### 文件: `src/services/CategoryService.js`

**代码文件功能说明**:
- 目录业务逻辑处理服务
- 处理复杂的目录操作逻辑
- 验证业务规则和数据完整性

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/models/Category.js` 文件中的 `Category` 类及其所有方法 (`getAllCategories`, `getCategoryById`, `createCategory`, `updateCategory`, `deleteCategory`, `updateCategoryOrder`)
- `src/models/Note.js` 文件中的 `getNotesByCategory` 函数 (用于删除目录前验证是否包含笔记)
- `src/utils/validator.js` 文件中的 `validateCategoryData` 函数 (用于目录数据验证)
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录服务层操作日志)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `CategoryService`: 目录服务类
- `getAllCategoriesWithCount()`: 获取目录及笔记数量
- `createCategoryWithValidation(data)`: 创建目录并验证
- `updateCategoryWithValidation(id, data)`: 更新目录并验证
- `deleteCategoryWithCheck(id)`: 安全删除目录
- `reorderCategories(orders)`: 批量重排序目录

#### 文件: `src/services/NoteService.js`

**代码文件功能说明**:
- 笔记业务逻辑处理服务
- 处理复杂的笔记操作逻辑
- 实现搜索、筛选等高级功能

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/models/Note.js` 文件中的 `Note` 类及其所有方法 (`getAllNotes`, `getNotesByCategory`, `getNoteById`, `createNote`, `updateNote`, `deleteNote`, `updateNoteOrder`, `toggleNoteImportance`)
- `src/models/Category.js` 文件中的 `getCategoryById` 函数 (用于验证笔记所属目录是否存在)
- `src/utils/validator.js` 文件中的 `validateNoteData` 函数 (用于笔记数据验证)
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录服务层操作日志)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `NoteService`: 笔记服务类
- `getNotesByCategoryWithPagination(categoryId, page, limit)`: 分页获取笔记
- `createNoteWithValidation(data)`: 创建笔记并验证
- `updateNoteWithValidation(id, data)`: 更新笔记并验证
- `deleteNoteWithCheck(id)`: 安全删除笔记
- `reorderNotes(categoryId, orders)`: 批量重排序笔记
- `searchNotes(keyword, categoryId)`: 搜索笔记
- `getImportantNotes()`: 获取重要笔记

---

### 3.5 控制器层

#### 文件: `src/controllers/CategoryController.js`

**代码文件功能说明**:
- 目录相关的HTTP请求处理控制器
- 处理请求参数验证和响应格式化
- 调用服务层处理业务逻辑

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/services/CategoryService.js` 文件中的 `CategoryService` 类及其所有方法 (`getAllCategoriesWithCount`, `createCategoryWithValidation`, `updateCategoryWithValidation`, `deleteCategoryWithCheck`, `reorderCategories`)
- `src/utils/responseHandler.js` 文件中的 `sendSuccess` 函数 (成功响应处理) 和 `sendError` 函数 (错误响应处理)
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录控制器操作日志)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `CategoryController`: 目录控制器类
- `getAllCategories`: 获取所有目录的控制器方法
- `getCategoryById`: 获取单个目录的控制器方法
- `createCategory`: 创建目录的控制器方法
- `updateCategory`: 更新目录的控制器方法
- `deleteCategory`: 删除目录的控制器方法
- `reorderCategories`: 重排序目录的控制器方法

#### 文件: `src/controllers/NoteController.js`

**代码文件功能说明**:
- 笔记相关的HTTP请求处理控制器
- 处理请求参数验证和响应格式化
- 调用服务层处理业务逻辑

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/services/NoteService.js` 文件中的 `NoteService` 类及其所有方法 (`getNotesByCategoryWithPagination`, `createNoteWithValidation`, `updateNoteWithValidation`, `deleteNoteWithCheck`, `reorderNotes`, `searchNotes`, `getImportantNotes`)
- `src/utils/responseHandler.js` 文件中的 `sendSuccess` 函数 (成功响应处理) 和 `sendError` 函数 (错误响应处理)
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录控制器操作日志)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `NoteController`: 笔记控制器类
- `getNotesByCategory`: 获取目录下笔记的控制器方法
- `getNoteById`: 获取单个笔记的控制器方法
- `createNote`: 创建笔记的控制器方法
- `updateNote`: 更新笔记的控制器方法
- `deleteNote`: 删除笔记的控制器方法
- `reorderNotes`: 重排序笔记的控制器方法
- `toggleImportance`: 切换重要性的控制器方法
- `searchNotes`: 搜索笔记的控制器方法

---

### 3.6 路由层

#### 文件: `src/routes/index.js`

**代码文件功能说明**:
- 应用程序的主路由配置文件
- 整合所有模块的路由
- 设置路由前缀和中间件

**代码文件依赖的需要格外安装的库**:
```json
{
  "express": "^4.18.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/routes/categoryRoutes.js` 文件中的 `categoryRouter` 变量 (Express目录路由器实例)
- `src/routes/noteRoutes.js` 文件中的 `noteRouter` 变量 (Express笔记路由器实例)
- `src/middleware/requestLogger.js` 文件中的 `requestLogger` 函数 (HTTP请求日志记录中间件)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `router`: Express路由器实例

#### 文件: `src/routes/categoryRoutes.js`

**代码文件功能说明**:
- 目录相关的路由定义
- 定义目录CRUD操作的API端点
- 应用相关的中间件

**代码文件依赖的需要格外安装的库**:
```json
{
  "express": "^4.18.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/controllers/CategoryController.js` 文件中的 `CategoryController` 类及其所有控制器方法 (`getAllCategories`, `getCategoryById`, `createCategory`, `updateCategory`, `deleteCategory`, `reorderCategories`)
- `src/middleware/validation.js` 文件中的 `validateCategoryInput` 函数 (目录输入数据验证中间件)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `categoryRouter`: 目录路由器实例

#### 文件: `src/routes/noteRoutes.js`

**代码文件功能说明**:
- 笔记相关的路由定义
- 定义笔记CRUD操作的API端点
- 应用相关的中间件

**代码文件依赖的需要格外安装的库**:
```json
{
  "express": "^4.18.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/controllers/NoteController.js` 文件中的 `NoteController` 类及其所有控制器方法 (`getNotesByCategory`, `getNoteById`, `createNote`, `updateNote`, `deleteNote`, `reorderNotes`, `toggleImportance`, `searchNotes`)
- `src/middleware/validation.js` 文件中的 `validateNoteInput` 函数 (笔记输入数据验证中间件)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `noteRouter`: 笔记路由器实例

---

### 3.7 中间件

#### 文件: `src/middleware/errorHandler.js`

**代码文件功能说明**:
- 全局错误处理中间件
- 统一错误响应格式
- 记录错误日志

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录错误日志信息)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `errorHandler`: 错误处理中间件函数

#### 文件: `src/middleware/validation.js`

**代码文件功能说明**:
- 请求数据验证中间件
- 验证请求参数和请求体
- 返回统一的验证错误响应

**代码文件依赖的需要格外安装的库**:
```json
{
  "joi": "^17.9.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/utils/responseHandler.js` 文件中的 `sendError` 函数 (用于返回统一格式的验证错误响应)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `validateCategoryInput`: 目录数据验证中间件
- `validateNoteInput`: 笔记数据验证中间件
- `validateId`: ID参数验证中间件

#### 文件: `src/middleware/corsOptions.js`

**代码文件功能说明**:
- CORS跨域配置中间件
- 设置允许的域名、方法和头部
- 处理预检请求

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- 无依赖

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `corsOptions`: CORS配置对象

#### 文件: `src/middleware/requestLogger.js`

**代码文件功能说明**:
- HTTP请求日志记录中间件
- 记录请求信息和响应时间
- 提供API调用统计数据

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/utils/logger.js` 文件中的 `logger` 对象 (Winston日志记录器实例，用于记录HTTP请求和响应信息)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `requestLogger`: 请求日志中间件函数

---

### 3.8 工具函数

#### 文件: `src/utils/logger.js`

**代码文件功能说明**:
- 应用程序日志记录工具
- 配置不同级别的日志输出
- 支持文件和控制台输出

**代码文件依赖的需要格外安装的库**:
```json
{
  "winston": "^3.10.0",
  "winston-daily-rotate-file": "^4.7.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- 无依赖

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `logger`: Winston日志记录器实例
- `logInfo(message, meta)`: 信息日志记录函数
- `logError(message, error)`: 错误日志记录函数
- `logWarn(message, meta)`: 警告日志记录函数

#### 文件: `src/utils/responseHandler.js`

**代码文件功能说明**:
- 统一的HTTP响应处理工具
- 标准化成功和错误响应格式
- 提供常用的HTTP状态码处理

**代码文件依赖的需要格外安装的库**:
- 无额外依赖

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- 无依赖

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `sendSuccess(res, data, message, statusCode)`: 成功响应处理函数
- `sendError(res, message, statusCode, error)`: 错误响应处理函数
- `sendCreated(res, data, message)`: 创建成功响应函数
- `sendNoContent(res)`: 无内容响应函数

#### 文件: `src/utils/validator.js`

**代码文件功能说明**:
- 数据验证工具函数
- 业务逻辑验证规则
- 输入数据格式化和清理

**代码文件依赖的需要格外安装的库**:
```json
{
  "joi": "^17.9.2",
  "validator": "^13.9.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- 无依赖

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `validateCategoryData(data)`: 目录数据验证函数
- `validateNoteData(data)`: 笔记数据验证函数
- `sanitizeInput(input)`: 输入数据清理函数
- `validateId(id)`: ID格式验证函数

---

### 3.9 配置文件

#### 文件: `src/config/config.js`

**代码文件功能说明**:
- 应用程序配置管理
- 环境变量读取和默认值设置
- 不同环境的配置切换

**代码文件依赖的需要格外安装的库**:
```json
{
  "dotenv": "^16.3.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- 无依赖

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `config`: 配置对象
- `PORT`: 服务器端口号
- `DB_PATH`: 数据库文件路径
- `LOG_LEVEL`: 日志级别
- `CORS_ORIGIN`: 允许的跨域源

---

## 4. API接口设计

### 4.1 目录管理接口

#### 4.1.1 获取所有目录
- **接口**: `GET /api/categories`
- **功能**: 获取所有目录列表，包含笔记数量
- **响应示例**:
```json
{
  "success": true,
  "message": "获取目录成功",
  "data": [
    {
      "id": 1,
      "name": "项目A",
      "description": "项目A的笔记",
      "sort_order": 1,
      "note_count": 5,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### 4.1.2 获取单个目录
- **接口**: `GET /api/categories/:id`
- **功能**: 根据ID获取目录详情

#### 4.1.3 创建目录
- **接口**: `POST /api/categories`
- **功能**: 创建新目录
- **请求体**:
```json
{
  "name": "新目录",
  "description": "目录描述"
}
```

#### 4.1.4 更新目录
- **接口**: `PUT /api/categories/:id`
- **功能**: 更新目录信息

#### 4.1.5 删除目录
- **接口**: `DELETE /api/categories/:id`
- **功能**: 删除目录（需检查是否有笔记）

#### 4.1.6 重排序目录
- **接口**: `PUT /api/categories/reorder`
- **功能**: 批量更新目录排序
- **请求体**:
```json
{
  "orders": [
    {"id": 1, "sort_order": 2},
    {"id": 2, "sort_order": 1}
  ]
}
```

### 4.2 笔记管理接口

#### 4.2.1 获取目录下的笔记
- **接口**: `GET /api/notes/category/:categoryId`
- **功能**: 获取指定目录下的所有笔记
- **查询参数**: `page`, `limit`, `important`

#### 4.2.2 获取单个笔记
- **接口**: `GET /api/notes/:id`
- **功能**: 根据ID获取笔记详情

#### 4.2.3 创建笔记
- **接口**: `POST /api/notes`
- **功能**: 创建新笔记
- **请求体**:
```json
{
  "category_id": 1,
  "title": "笔记标题",
  "content": "笔记内容",
  "is_important": false
}
```

#### 4.2.4 更新笔记
- **接口**: `PUT /api/notes/:id`
- **功能**: 更新笔记内容

#### 4.2.5 删除笔记
- **接口**: `DELETE /api/notes/:id`
- **功能**: 删除笔记

#### 4.2.6 切换重要性标记
- **接口**: `PUT /api/notes/:id/toggle-importance`
- **功能**: 切换笔记的重要性标记

#### 4.2.7 重排序笔记
- **接口**: `PUT /api/notes/reorder`
- **功能**: 批量更新笔记排序

#### 4.2.8 搜索笔记
- **接口**: `GET /api/notes/search`
- **功能**: 根据关键词搜索笔记
- **查询参数**: `keyword`, `category_id`

## 5. 接口测试模块

### 5.1 测试配置文件

#### 文件: `tests/setup.js`

**代码文件功能说明**:
- Jest测试框架的全局配置
- 测试数据库的初始化和清理
- 测试环境的设置

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1",
  "supertest": "^6.3.3"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/config/database.js` 文件中的 `initDatabase` 函数 (初始化测试数据库) 和 `closeDatabase` 函数 (关闭数据库连接)
- `src/app.js` 文件中的 `app` 变量 (Express应用实例，用于测试HTTP请求)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `setupTestDatabase()`: 初始化测试数据库函数
- `cleanupTestDatabase()`: 清理测试数据库函数

### 5.2 目录接口测试

#### 文件: `tests/controllers/categoryController.test.js`

**代码文件功能说明**:
- 目录控制器的完整测试套件
- 测试所有目录相关的API端点
- 包含正常情况和异常情况的测试

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1",
  "supertest": "^6.3.3"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `tests/setup.js` 文件中的 `setupTestDatabase` 函数 (初始化测试数据库) 和 `cleanupTestDatabase` 函数 (清理测试数据库)
- `src/app.js` 文件中的 `app` 变量 (Express应用实例，用于执行HTTP测试请求)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `createTestCategory(data)`: 创建测试目录函数
- `deleteTestCategory(id)`: 删除测试目录函数

### 5.3 笔记接口测试

#### 文件: `tests/controllers/noteController.test.js`

**代码文件功能说明**:
- 笔记控制器的完整测试套件
- 测试所有笔记相关的API端点
- 包含数据验证和业务逻辑测试

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1",
  "supertest": "^6.3.3"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `tests/setup.js` 文件中的 `setupTestDatabase` 函数 (初始化测试数据库) 和 `cleanupTestDatabase` 函数 (清理测试数据库)
- `src/app.js` 文件中的 `app` 变量 (Express应用实例，用于执行HTTP测试请求)
- `tests/controllers/categoryController.test.js` 文件中的 `createTestCategory` 函数 (创建测试目录，用于笔记测试的前置条件)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- `createTestNote(data)`: 创建测试笔记函数
- `deleteTestNote(id)`: 删除测试笔记函数

### 5.4 数据模型测试

#### 文件: `tests/models/Category.test.js`

**代码文件功能说明**:
- 目录数据模型的单元测试
- 测试数据库操作方法
- 验证数据完整性和约束

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `tests/setup.js` 文件中的 `setupTestDatabase` 函数 (初始化测试数据库) 和 `cleanupTestDatabase` 函数 (清理测试数据库)
- `src/models/Category.js` 文件中的 `Category` 类及其所有方法 (`getAllCategories`, `getCategoryById`, `createCategory`, `updateCategory`, `deleteCategory`, `updateCategoryOrder`)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- 无对外暴露函数（纯测试文件）

#### 文件: `tests/models/Note.test.js`

**代码文件功能说明**:
- 笔记数据模型的单元测试
- 测试数据库操作方法
- 验证外键约束和数据关系

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `tests/setup.js` 文件中的 `setupTestDatabase` 函数 (初始化测试数据库) 和 `cleanupTestDatabase` 函数 (清理测试数据库)
- `src/models/Note.js` 文件中的 `Note` 类及其所有方法 (`getAllNotes`, `getNotesByCategory`, `getNoteById`, `createNote`, `updateNote`, `deleteNote`, `updateNoteOrder`, `toggleNoteImportance`)
- `src/models/Category.js` 文件中的 `createCategory` 函数 (用于创建测试笔记所需的目录)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- 无对外暴露函数（纯测试文件）

### 5.5 服务层测试

#### 文件: `tests/services/CategoryService.test.js`

**代码文件功能说明**:
- 目录服务层的业务逻辑测试
- 测试复杂的业务规则和验证
- 模拟各种业务场景

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `tests/setup.js` 文件中的 `setupTestDatabase` 函数 (初始化测试数据库) 和 `cleanupTestDatabase` 函数 (清理测试数据库)
- `src/services/CategoryService.js` 文件中的 `CategoryService` 类及其所有方法 (`getAllCategoriesWithCount`, `createCategoryWithValidation`, `updateCategoryWithValidation`, `deleteCategoryWithCheck`, `reorderCategories`)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- 无对外暴露函数（纯测试文件）

#### 文件: `tests/services/NoteService.test.js`

**代码文件功能说明**:
- 笔记服务层的业务逻辑测试
- 测试搜索、排序等高级功能
- 验证数据关系和约束

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `tests/setup.js` 文件中的 `setupTestDatabase` 函数 (初始化测试数据库) 和 `cleanupTestDatabase` 函数 (清理测试数据库)
- `src/services/NoteService.js` 文件中的 `NoteService` 类及其所有方法 (`getNotesByCategoryWithPagination`, `createNoteWithValidation`, `updateNoteWithValidation`, `deleteNoteWithCheck`, `reorderNotes`, `searchNotes`, `getImportantNotes`)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- 无对外暴露函数（纯测试文件）

### 5.6 集成测试

#### 文件: `tests/integration/api.test.js`

**代码文件功能说明**:
- 完整的API集成测试
- 测试多个模块之间的交互
- 模拟真实的用户操作流程

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1",
  "supertest": "^6.3.3"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `tests/setup.js` 文件中的 `setupTestDatabase` 函数 (初始化测试数据库) 和 `cleanupTestDatabase` 函数 (清理测试数据库)
- `src/app.js` 文件中的 `app` 变量 (Express应用实例，用于执行完整的API集成测试)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- 无对外暴露函数（纯测试文件）

### 5.7 工具函数测试

#### 文件: `tests/utils/validator.test.js`

**代码文件功能说明**:
- 验证工具函数的单元测试
- 测试各种输入验证规则
- 边界条件和异常情况测试

**代码文件依赖的需要格外安装的库**:
```json
{
  "jest": "^29.6.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**:
- `src/utils/validator.js` 文件中的所有验证函数 (`validateCategoryData`, `validateNoteData`, `sanitizeInput`, `validateId`)

**当前代码文件可以提供给其他代码文件引用的变量、函数**:
- 无对外暴露函数（纯测试文件）

## 6. 项目依赖包配置

### 6.1 生产依赖

```json
{
  "express": "^4.18.2",
  "sqlite3": "^5.1.6",
  "cors": "^2.8.5",
  "helmet": "^7.0.0",
  "compression": "^1.7.4",
  "express-rate-limit": "^6.8.1",
  "dotenv": "^16.3.1",
  "winston": "^3.10.0",
  "winston-daily-rotate-file": "^4.7.1",
  "joi": "^17.9.2",
  "validator": "^13.9.0"
}
```

### 6.2 开发依赖

```json
{
  "jest": "^29.6.1",
  "supertest": "^6.3.3",
  "nodemon": "^3.0.1",
  "@types/jest": "^29.5.3",
  "eslint": "^8.45.0",
  "prettier": "^3.0.0"
}
```

## 7. 部署和运行配置

### 7.1 npm scripts

```json
{
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix",
    "format": "prettier --write src/"
  }
}
```

### 7.2 环境变量配置

```env
# 服务器配置
PORT=1315
NODE_ENV=development

# 数据库配置
DB_PATH=./data/notes.db

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs

# CORS配置
CORS_ORIGIN=http://localhost:1314

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 8. API文档

### 8.1 响应格式标准

#### 成功响应格式：
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### 错误响应格式：
```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误描述",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 8.2 状态码说明

- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误

### 8.3 分页参数

```
page: 页码，从1开始 (默认: 1)
limit: 每页条数 (默认: 10, 最大: 100)
```

### 8.4 排序参数

```
sort_field: 排序字段 (created_at, updated_at, sort_order)
sort_order: 排序方向 (asc, desc)
```

## 9. 总结

本后端框架设计遵循MVC架构模式，分层清晰，职责明确。主要特点：

1. **模块化设计**: 每个功能模块独立，便于维护和扩展
2. **RESTful API**: 遵循REST设计规范，接口清晰易用
3. **完整测试覆盖**: 包含单元测试、集成测试和API测试
4. **错误处理**: 统一的错误处理机制和响应格式
5. **日志记录**: 完善的日志记录和监控
6. **数据验证**: 多层数据验证确保数据安全
7. **可扩展性**: 预留扩展接口，支持后续功能增加

该框架可以完全支持前端产品原型中定义的所有功能需求，包括目录管理、笔记管理、排序、重要性标记等核心功能。
