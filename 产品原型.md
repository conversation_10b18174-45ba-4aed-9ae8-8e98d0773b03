# 项目笔记管理系统 - 产品原型文档

## 1. 产品概述

### 1.1 产品名称
项目笔记管理系统

### 1.2 产品定位
一款面向开发者和项目管理人员的在线笔记管理工具，专注于项目开发过程中关键知识点的记录、整理和查阅。

### 1.3 目标用户
- 软件开发工程师
- 项目经理
- 技术团队负责人
- 需要记录项目要点的相关人员

### 1.4 核心价值
- 结构化管理项目笔记
- 快速检索和编辑
- 高效的知识复用

## 2. 功能架构

### 2.1 系统架构
```
项目笔记管理系统
├── 目录管理模块
│   ├── 目录创建
│   ├── 目录排序
│   └── 目录选择
└── 笔记管理模块
    ├── 笔记创建
    ├── 笔记编辑
    ├── 笔记排序
    ├── 重点标记
    └── 内容复制
```

### 2.2 技术栈
- **前端**: Vue.js 3 + Vite + Element Plus
- **数据存储**: SQLite3 (Node.js sqlite3 模块)
- **部署环境**: Web 浏览器

## 3. 功能模块详设

### 3.1 目录管理模块

#### 3.1.1 页面布局
- **布局方式**: 左侧固定宽度导航栏 + 右侧自适应内容区
- **响应式**: 支持桌面端使用，宽度不小于 1024px

#### 3.1.2 目录导航区功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 目录列表展示 | 垂直排列所有用户创建的目录 | P0 |
| 目录选中状态 | 当前选中目录高亮显示 | P0 |
| 默认选中 | 页面加载时自动选中第一个目录 | P0 |
| 新增目录 | 点击"+"图标按钮可创建新目录 | P0 |
| 异步内容加载 | 选中目录时异步加载对应的笔记内容 | P0 |
| 目录排序 | 每个目录配备上移/下移按钮 | P1 |
| 目录编辑 | 支持目录名称的修改 | P1 |
| 目录删除 | 支持目录的删除操作 | P2 |

#### 3.1.3 交互规范
- **选中效果**: 选中目录背景色变化，左侧边框高亮
- **悬停效果**: 鼠标悬停时显示操作按钮
- **排序按钮**: 使用上下箭头图标，仅在悬停时显示
- **异步加载**: 点击目录后，右侧内容区显示加载状态，完成后展示对应笔记
- **新增按钮**: 使用"+"图标，位置固定在目录列表顶部

### 3.2 笔记管理模块

#### 3.2.1 内容展示区功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 笔记列表 | 展示当前目录下所有笔记 | P0 |
| 笔记结构 | 每条笔记包含标题和正文 | P0 |
| 即时编辑 | 点击标题/正文即可编辑 | P0 |
| 手动保存 | 编辑后通过保存按钮手动保存 | P0 |
| 保存状态提示 | 编辑时底色变化，显示未保存状态 | P0 |
| 重点标记 | 支持标记重要笔记 | P1 |
| 内容复制 | 一键复制正文内容 | P1 |
| 笔记排序 | 调整笔记在目录内的順序 | P1 |
| 笔记删除 | 删除不需要的笔记 | P2 |

#### 3.2.2 交互规范
- **编辑模式**: 点击后边框变化，显示文本光标
- **编辑状态提示**: 内容改动时，编辑区域底色变为特殊颜色（如浅黄色）
- **保存按钮**: 每个编辑区域都有隐藏的保存按钮，默认不显示
- **保存按钮显示**: 鼠标离开编辑框区域后，如有未保存更改则显示保存按钮
- **保存反馈**: 点击保存成功后，按钮消失，底色恢复正常，短暂显示保存成功提示
- **重点标记**: 使用星标图标，标记后笔记整体高亮
- **复制按钮**: 位于正文区域旁边，使用剪贴板图标
- **排序按钮**: 每条笔记都配备上移/下移控制按钮

## 4. 页面原型设计

### 4.1 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│                    项目笔记管理系统                        │
├─────────────┬───────────────────────────────────────────┤
│   目录导航   │              内容展示区                    │
│             │                                           │
│ [+] 新增     │  ┌─────────────────────────────────────┐  │
│             │  │ 📌 笔记标题 1              [↑][↓]  │  │
│ • 项目A     │  │    这里是笔记正文内容...      [📋]   │  │
│ ○ 项目B     │  └─────────────────────────────────────┘  │
│ ○ 项目C     │                                           │
│   [↑][↓]    │  ┌─────────────────────────────────────┐  │
│             │  │ ⭐ 重要笔记标题            [↑][↓]  │  │
│             │  │    重要的笔记内容...        [📋]   │  │
│             │  └─────────────────────────────────────┘  │
│             │                                           │
│             │              [+ 添加笔记]                  │
└─────────────┴───────────────────────────────────────────┘
```

### 4.2 编辑状态示意
```
编辑前（正常状态）:
┌─────────────────────────────────────┐
│ 📌 项目需求分析            [↑][↓]  │
│    分析用户需求和功能点...    [📋]   │
└─────────────────────────────────────┘

编辑中（底色变化，表示有未保存更改）:
┌─────────────────────────────────────┐ ← 浅黄色背景
│ 📌 项目需求分析和设计       [↑][↓]  │
│    分析用户需求和功能点...    [📋]   │
└─────────────────────────────────────┘

离开编辑框后（显示保存按钮）:
┌─────────────────────────────────────┐ ← 浅黄色背景
│ 📌 项目需求分析和设计    [💾] [↑][↓] │
│    分析用户需求和功能点...    [📋]   │
└─────────────────────────────────────┘

保存后（恢复正常）:
┌─────────────────────────────────────┐
│ 📌 项目需求分析和设计       [↑][↓]  │
│    分析用户需求和功能点...    [📋]   │
└─────────────────────────────────────┘
```

### 4.3 状态说明
- `•` 表示当前选中的目录
- `○` 表示未选中的目录
- `📌` 表示普通笔记
- `⭐` 表示重点标记的笔记
- `[↑][↓]` 表示排序按钮
- `[📋]` 表示复制按钮
- `[💾]` 表示保存按钮（仅在有未保存更改时显示）

## 5. 用户故事

### 5.1 目录管理
- **作为**开发者，**我希望**能创建不同的项目目录，**以便于**分类管理不同项目的笔记
- **作为**用户，**我希望**能调整目录顺序，**以便于**按照项目优先级排列
- **作为**用户，**我希望**选中目录时有明显的视觉反馈，**以便于**确认当前操作的对象
- **作为**用户，**我希望**切换目录时内容能快速加载，**以便于**提高使用效率

### 5.2 笔记管理  
- **作为**开发者，**我希望**能快速记录和编辑笔记，**以便于**及时记录开发要点
- **作为**用户，**我希望**能标记重要笔记，**以便于**快速识别关键信息
- **作为**用户，**我希望**能一键复制笔记内容，**以便于**在其他地方引用
- **作为**用户，**我希望**编辑时有明显的视觉提示，**以便于**知道内容尚未保存
- **作为**用户，**我希望**能手动控制保存时机，**以便于**避免误操作和确保内容准确
- **作为**用户，**我希望**能调整笔记顺序，**以便于**按重要程度或时间排列

## 6. 验收标准

### 6.1 功能性验收
- [ ] 能够创建、编辑、删除目录
- [ ] 能够创建、编辑、删除笔记
- [ ] 目录和笔记的排序功能正常
- [ ] 手动保存功能稳定可靠
- [ ] 编辑状态的视觉提示正确显示
- [ ] 保存按钮的显示和隐藏逻辑正确
- [ ] 重点标记和复制功能正常

### 6.2 易用性验收
- [ ] 界面操作直观，无需说明即可上手
- [ ] 编辑模式切换流畅
- [ ] 页面响应速度 < 500ms
- [ ] 支持常用快捷键操作

### 6.3 兼容性验收
- [ ] Chrome 90+ 浏览器兼容
- [ ] Firefox 88+ 浏览器兼容
- [ ] Safari 14+ 浏览器兼容
- [ ] 屏幕分辨率 1024px 以上正常显示

## 7. 开发优先级

### MVP 版本 (P0 功能)
1. 基础页面布局
2. 目录创建和选择
3. 异步数据加载机制
4. 笔记的创建和编辑
5. 手动保存功能
6. 编辑状态视觉提示

### V1.1 版本 (P1 功能)
1. 目录和笔记排序
2. 重点标记功能
3. 内容复制功能
4. 基础的删除操作

### V1.2 版本 (P2 功能)
1. 批量操作
2. 搜索功能
3. 导入导出功能
4. 用户偏好设置

## 8. 风险评估

### 8.1 技术风险
- **数据持久化**: SQLite3 在浏览器环境的兼容性需要验证
- **性能风险**: 大量笔记时的加载和渲染性能
- **数据安全**: 本地数据的备份和恢复机制
- **异步加载**: 目录切换时的数据加载性能和错误处理


### 8.2 用户体验风险
- **学习成本**: 用户对即时编辑模式的适应度
- **手动保存**: 用户可能忘记保存导致数据丢失
- **视觉提示**: 编辑状态的底色变化可能不够明显
- **保存按钮位置**: 保存按钮的显示时机和位置需要用户习惯
- **数据丢失**: 浏览器意外关闭时未保存的编辑内容会丢失
