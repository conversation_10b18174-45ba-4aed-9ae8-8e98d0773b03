<context>
# Overview  
Material Quotation System is a web-based application for managing material pricing and quotations. It supports multiple material types with different pricing models (per piece, per length, per area), organizes materials by rooms, and generates editable Excel quotation sheets. The system is designed as a single-page application using Vue.js 3 with local SQLite database storage, requiring no backend server.

# Core Features  
1. **Material Management**: 
   - Support three pricing types: per piece, per length, per area
   - Material properties: name, type, unit price, unit, description
   - CRUD operations for material library

2. **Quotation Creation**:
   - Direct material quotation: Add materials directly to main interface
   - Room-based quotation: Create rooms and add materials to rooms
   - Real-time price calculation with transparent formulas

3. **Calculation Engine**:
   - Support addition operations (default)
   - Support subtraction operations (e.g., wall area minus door/window area)
   - Manual editing of all values
   - Real-time calculation updates
   - Formula transparency and editability

4. **Excel Export**:
   - Generate detailed quotation sheets
   - Preserve Excel formulas for post-export editing
   - Professional formatting and styling

# User Experience  
- **Primary Users**: Interior designers, contractors, material suppliers
- **Key User Flows**: 
  1. Material library setup → Room creation → Material assignment → Quotation generation → Excel export
  2. Direct material quotation → Price calculation → Excel export
- **UI/UX**: Clean, intuitive interface with real-time calculations and clear formula display
</context>
<PRD>
# Technical Architecture  
- **Frontend**: Vue.js 3 + Vite build system
- **UI Framework**: Element Plus component library
- **State Management**: Pinia for centralized state management
- **Routing**: Vue Router 4 for SPA navigation
- **Database**: SQL.js (SQLite in browser) for local data storage
- **Excel Processing**: SheetJS (xlsx) library for Excel generation
- **Styling**: CSS3 with responsive design
- **Build Tool**: Vite for fast development and optimized builds

# Development Roadmap  

## Phase 1: Foundation & Core Infrastructure (MVP)
- Project setup with Vue 3 + Vite + Element Plus
- Basic routing and navigation structure
- Database schema design and SQL.js integration
- Core utility functions (database operations, calculations, validators)
- State management setup with Pinia stores
- Basic layout components (header, sidebar, footer)

## Phase 2: Material Management System
- Material data model and database operations
- Material management interface (CRUD operations)
- Material type handling (piece/length/area pricing)
- Material search and filtering functionality
- Material validation and error handling

## Phase 3: Room Management System
- Room data model and database operations
- Room creation and management interface
- Room-material association functionality
- Room calculation engine (addition/subtraction operations)
- Room preview and editing capabilities

## Phase 4: Quotation Engine
- Quotation data model and workflow
- Direct material quotation interface
- Room-based quotation interface
- Real-time calculation engine
- Formula display and editing
- Quotation preview and validation

## Phase 5: Excel Export System
- Excel template design and structure
- SheetJS integration for Excel generation
- Formula preservation in exported files
- Professional formatting and styling
- Export validation and error handling

## Phase 6: Testing & Polish
- Unit tests for core functions
- Integration tests for user workflows
- Cross-browser compatibility testing
- Performance optimization
- UI/UX refinements and accessibility improvements

# Logical Dependency Chain
1. **Foundation First**: Project setup, routing, and basic components must be established before any feature development
2. **Database Layer**: Core database operations and data models are prerequisites for all business logic
3. **Material Management**: Must be completed before room management since rooms depend on materials
4. **Room Management**: Required before room-based quotations can be implemented
5. **Calculation Engine**: Core to both material and room management, should be developed in parallel
6. **Quotation System**: Depends on both material and room management being functional
7. **Excel Export**: Final layer that depends on all other systems being complete
8. **Testing**: Continuous throughout development but comprehensive testing at the end

# Risks and Mitigations  
- **Technical Challenges**: 
  - Excel formula preservation complexity → Deep research into SheetJS advanced features
  - Cross-platform compatibility → Extensive testing on different browsers and OS
  - Local storage limitations → Design flexible data architecture for future upgrades

- **MVP Definition**: 
  - Focus on core material management and basic quotation functionality first
  - Excel export can be simplified initially (basic formatting without complex formulas)
  - Room management can be phase 2 if needed to accelerate MVP delivery

- **Resource Constraints**: 
  - Prioritize core functionality over advanced features
  - Use established libraries (Element Plus, SheetJS) to reduce development time
  - Implement comprehensive error handling to ensure stability

# Appendix  
- **Data Models**: JSON-based structures for materials, rooms, and quotations
- **Technology Stack**: Modern web technologies with proven libraries
- **Deployment**: Static file deployment, no server requirements
- **Future Enhancements**: Template system, history management, cloud sync capabilities
</PRD>
