# 项目笔记网站应用功能描述

## 1. 项目概述
开发一款项目笔记网站应用，主要用于记录项目开发过程中的关键要点。

## 2. 技术选型
- **前端框架**: Vue.js 3 + Vite
- **UI组件库**: Element Plus
- **数据库**: SQLite3 (通过 Node.js 的 `sqlite3` 模块进行操作)
- **运行环境**: Web浏览器

## 3. 功能设计

### 3.1 页面布局
网页整体采用左右分栏布局：
- **左侧**: 目录导航区
- **右侧**: 内容展示区

### 3.2 目录导航区 (左侧)
- **目录结构**: 从上至下垂直排列用户自定义的目录。
- **排序功能**: 每个目录条目右侧配有“上移”和“下移”的图标按钮，方便用户自由调整目录顺序。
- **交互效果**: 
    - 当用户选中某个目录时，该目录应有高亮效果以突出显示。
    - 应用首次加载时，默认选中第一个目录。
- **内容联动**: 右侧内容区需要异步加载并展示当前选中目录下的所有笔记。
- **新增目录**: 导航区提供一个“新增目录”的图标按钮，点击后可以创建新的目录条目。

### 3.3 内容展示区 (右侧)
- **笔记列表**: 从上至下垂直排列当前目录下的每一条笔记。
- **笔记结构**: 每条笔记由“标题”和“正文”两部分组成。
- **重点标记**: 用户可以对重要的笔记条目进行“重点标记”，被标记的笔记应有突出的视觉样式。
- **即时编辑与手动保存**:
    - 笔记的标题和正文都支持"点击即编辑"模式，用户点击后即可进入编辑状态。
    - 每个编辑区域都有一个隐藏的保存按钮。
    - 当内容发生改动时，编辑区域底色会特殊显示以提示用户有未保存的更改。
    - 鼠标离开编辑框区域后，保存按钮会显示出来供用户点击。
    - 用户点击保存成功后，保存按钮消失，编辑区域底色恢复正常。项目笔记网站应用，主要用于记录项目开发过程中的关键要点。
- **便捷操作**:
    - 每条笔记的正文部分旁边，提供一个“复制”图标按钮，方便用户一键将正文内容复制到剪切板。
    - 每条笔记也需提供“上移”和“下移”的控制按钮，用于调整其在当前目录内的排序。
