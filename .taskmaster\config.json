{"models": {"main": {"provider": "openrouter", "modelId": "deepseek/deepseek-chat-v3-0324:free", "maxTokens": 160000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "moonshotai/kimi-k2:free", "maxTokens": 60000, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "deepseek/deepseek-chat-v3-0324:free", "maxTokens": 160000, "temperature": 0.2}}, "global": {"logLevel": "debug", "debug": true, "defaultNumTasks": 5, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "Chinese", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {}}