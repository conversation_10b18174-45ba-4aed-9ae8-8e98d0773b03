# 项目依赖环境安装说明

本说明梳理了前端与后端所有脚本所需单独安装的第三方依赖库，并给出一键安装命令。

---

## 一、前端依赖库

### 1. 生产依赖
- vue@^3.3.0
- vue-router@^4.2.0
- pinia@^2.1.0
- element-plus@^2.3.0
- @element-plus/icons-vue@^2.1.0
- axios@^1.5.0
- pinia-plugin-persistedstate@^3.2.0
- hotkeys-js@^3.10.0
- @wangeditor/editor@^5.1.0
- @wangeditor/editor-for-vue@^5.1.0

### 2. 开发依赖
- vite@^4.4.0
- @vitejs/plugin-vue@^4.3.0
- sass@^1.66.0
- unplugin-auto-import@^0.16.0
- unplugin-vue-components@^0.25.0
- vitest@^0.34.0
- @vue/test-utils@^2.4.0
- jsdom@^22.1.0
- eslint@^8.47.0
- prettier@^3.0.0
- @vue/eslint-config-prettier@^8.0.0

### 3. 前端一键安装命令
```sh
npm install vue@^3.3.0 vue-router@^4.2.0 pinia@^2.1.0 element-plus@^2.3.0 @element-plus/icons-vue@^2.1.0 axios@^1.5.0 pinia-plugin-persistedstate@^3.2.0 hotkeys-js@^3.10.0 @wangeditor/editor@^5.1.0 @wangeditor/editor-for-vue@^5.1.0
npm install -D vite@^4.4.0 @vitejs/plugin-vue@^4.3.0 sass@^1.66.0 unplugin-auto-import@^0.16.0 unplugin-vue-components@^0.25.0 vitest@^0.34.0 @vue/test-utils@^2.4.0 jsdom@^22.1.0 eslint@^8.47.0 prettier@^3.0.0 @vue/eslint-config-prettier@^8.0.0
```

---

## 二、后端依赖库

### 1. 生产依赖
- express
- sqlite3
- cors
- dotenv
- morgan

### 2. 开发依赖
- nodemon
- jest
- supertest
- eslint
- prettier

### 3. 后端一键安装命令
```sh
npm install express sqlite3 cors dotenv morgan
npm install -D nodemon jest supertest eslint prettier
```

---

## 三、说明
- 请分别在前端和后端项目根目录下执行上述命令。
- 如需使用yarn/pnpm等包管理工具，请将命令中的`npm install`替换为对应命令。
- 依赖版本可根据实际需要调整。
